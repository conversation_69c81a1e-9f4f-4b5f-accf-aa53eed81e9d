#!/usr/bin/env python3
"""
Script para alterar cores laranja para azul (#000646) em arquivos Excel
"""

import os
import glob
from openpyxl import load_workbook
from openpyxl.styles import <PERSON><PERSON>Fill, Font, Border, Side
from openpyxl.utils.cell import coordinate_from_string, column_index_from_string
import re

def rgb_to_hex(rgb_string):
    """Converte string RGB para hex"""
    if not rgb_string:
        return None
    # Remove 'FF' prefix se presente
    if len(rgb_string) == 8 and rgb_string.startswith('FF'):
        return rgb_string[2:]
    return rgb_string

def is_orange_color(color_hex):
    """Verifica se a cor é laranja (várias tonalidades)"""
    if not color_hex:
        return False
    
    # Lista de cores laranja comuns em hex
    orange_colors = [
        'FFA500',  # Orange
        'FF8C00',  # DarkOrange
        'FF7F50',  # Coral
        'FF6347',  # Tomato
        'FF4500',  # OrangeRed
        'FFA07A',  # LightSalmon
        'FF8000',  # Orange variant
        'FF9500',  # Orange variant
        'FFAA00',  # Orange variant
        'FF9900',  # Orange variant
        'CC6600',  # Dark orange
        'FF6600',  # Orange red
        'FF7700',  # Orange variant
        'FF8800',  # Orange variant
        'FFBB33',  # Light orange
        'FF9933',  # Orange variant
        'FF6633',  # Orange variant
        'E67E22',  # Orange (Bootstrap)
        'F39C12',  # Orange (Bootstrap)
        'D68910',  # Dark orange
        'DC7633',  # Light orange
    ]
    
    color_upper = color_hex.upper()
    return color_upper in orange_colors

def change_cell_colors(worksheet, target_color='000646'):
    """Altera cores laranja das células para azul"""
    changes_made = 0
    
    for row in worksheet.iter_rows():
        for cell in row:
            # Verificar cor de fundo
            if cell.fill and cell.fill.fill_type == 'solid':
                if cell.fill.start_color and cell.fill.start_color.rgb:
                    current_color = rgb_to_hex(cell.fill.start_color.rgb)
                    if is_orange_color(current_color):
                        cell.fill = PatternFill(start_color=target_color, end_color=target_color, fill_type='solid')
                        changes_made += 1
                        print(f"  Alterada cor de fundo da célula {cell.coordinate}: {current_color} -> {target_color}")
            
            # Verificar cor da fonte
            if cell.font and cell.font.color:
                if cell.font.color.rgb:
                    current_color = rgb_to_hex(cell.font.color.rgb)
                    if is_orange_color(current_color):
                        new_font = Font(
                            name=cell.font.name,
                            size=cell.font.size,
                            bold=cell.font.bold,
                            italic=cell.font.italic,
                            underline=cell.font.underline,
                            strike=cell.font.strike,
                            color=target_color
                        )
                        cell.font = new_font
                        changes_made += 1
                        print(f"  Alterada cor da fonte da célula {cell.coordinate}: {current_color} -> {target_color}")
            
            # Verificar bordas
            if cell.border:
                border_changed = False
                new_border_dict = {}
                
                for side_name in ['left', 'right', 'top', 'bottom', 'diagonal']:
                    side = getattr(cell.border, side_name)
                    if side and side.color and side.color.rgb:
                        current_color = rgb_to_hex(side.color.rgb)
                        if is_orange_color(current_color):
                            new_border_dict[side_name] = Side(style=side.style, color=target_color)
                            border_changed = True
                            changes_made += 1
                            print(f"  Alterada cor da borda {side_name} da célula {cell.coordinate}: {current_color} -> {target_color}")
                        else:
                            new_border_dict[side_name] = side
                    else:
                        new_border_dict[side_name] = side
                
                if border_changed:
                    cell.border = Border(**new_border_dict)
    
    return changes_made

def process_excel_file(file_path, target_color='000646'):
    """Processa um arquivo Excel alterando cores laranja para azul"""
    print(f"\nProcessando: {file_path}")
    
    try:
        workbook = load_workbook(file_path)
        total_changes = 0
        
        for sheet_name in workbook.sheetnames:
            print(f"  Processando planilha: {sheet_name}")
            worksheet = workbook[sheet_name]
            changes = change_cell_colors(worksheet, target_color)
            total_changes += changes
            
        if total_changes > 0:
            # Fazer backup do arquivo original
            backup_path = file_path + '.backup'
            if not os.path.exists(backup_path):
                import shutil
                shutil.copy2(file_path, backup_path)
                print(f"  Backup criado: {backup_path}")
            
            workbook.save(file_path)
            print(f"  ✓ Arquivo salvo com {total_changes} alterações")
        else:
            print(f"  ✓ Nenhuma cor laranja encontrada")
            
        workbook.close()
        return total_changes
        
    except Exception as e:
        print(f"  ✗ Erro ao processar {file_path}: {str(e)}")
        return 0

def main():
    """Função principal"""
    print("Iniciando alteração de cores laranja para azul (#000646) em arquivos Excel...")
    
    # Diretórios para processar
    directories = [
        'ModelosImportacao',
        'ModelosImportacao/Ecf',
        'ModelosRelatorios'
    ]
    
    total_files_processed = 0
    total_changes_made = 0
    
    for directory in directories:
        if os.path.exists(directory):
            print(f"\n=== Processando diretório: {directory} ===")
            
            # Buscar arquivos Excel
            excel_files = []
            excel_files.extend(glob.glob(os.path.join(directory, '*.xlsx')))
            excel_files.extend(glob.glob(os.path.join(directory, '*.xlsm')))
            excel_files.extend(glob.glob(os.path.join(directory, '*.xls')))
            
            for file_path in excel_files:
                changes = process_excel_file(file_path)
                total_files_processed += 1
                total_changes_made += changes
        else:
            print(f"Diretório não encontrado: {directory}")
    
    print(f"\n=== RESUMO ===")
    print(f"Arquivos processados: {total_files_processed}")
    print(f"Total de alterações: {total_changes_made}")
    print(f"Cor alvo: #000646 (azul)")
    
    if total_changes_made > 0:
        print(f"\n✓ Processo concluído com sucesso!")
        print(f"Backups dos arquivos originais foram criados com extensão .backup")
    else:
        print(f"\n✓ Processo concluído - nenhuma cor laranja foi encontrada nos arquivos.")

if __name__ == "__main__":
    main()
