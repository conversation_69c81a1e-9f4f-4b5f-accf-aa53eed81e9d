# Script PowerShell simples para alterar cores laranja para azul em um arquivo Excel

param(
    [Parameter(Mandatory=$true)]
    [string]$FilePath
)

Write-Host "Processando arquivo: $FilePath"

if (-not (Test-Path $FilePath)) {
    Write-Host "Arquivo não encontrado: $FilePath"
    exit 1
}

try {
    # Criar backup
    $backupPath = "$FilePath.backup"
    if (-not (Test-Path $backupPath)) {
        Copy-Item $FilePath $backupPath
        Write-Host "Backup criado: $backupPath"
    }
    
    # Abrir Excel
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false
    
    $workbook = $excel.Workbooks.Open($FilePath)
    $totalChanges = 0
    
    # Cor alvo: azul #000646 (R=0, G=6, B=70) em formato Long do Excel
    $targetColor = 4587008  # 0 + (6 * 256) + (70 * 65536)
    
    # Lista de cores laranja em formato Long do Excel
    $orangeColors = @(
        16753920,   # FFA500 - Orange
        9109504,    # FF8C00 - DarkOrange
        5275647,    # FF7F50 - Coral
        4678655,    # FF6347 - Tomato
        17919,      # FF4500 - OrangeRed
        8036607,    # FFA07A - LightSalmon
        32768,      # FF8000 - Orange variant
        38400,      # FF9500 - Orange variant
        43520,      # FFAA00 - Orange variant
        39168       # FF9900 - Orange variant
    )
    
    Write-Host "Processando $($workbook.Worksheets.Count) planilhas..."
    
    foreach ($worksheet in $workbook.Worksheets) {
        Write-Host "  Planilha: $($worksheet.Name)"
        $changes = 0
        
        $usedRange = $worksheet.UsedRange
        if ($null -ne $usedRange) {
            # Limitar processamento para evitar travamento
            $maxRows = [Math]::Min($usedRange.Rows.Count, 50)
            $maxCols = [Math]::Min($usedRange.Columns.Count, 20)
            
            Write-Host "    Processando $maxRows linhas x $maxCols colunas"
            
            for ($row = 1; $row -le $maxRows; $row++) {
                for ($col = 1; $col -le $maxCols; $col++) {
                    $cell = $usedRange.Cells.Item($row, $col)
                    
                    # Verificar cor de fundo
                    try {
                        $bgColor = $cell.Interior.Color
                        if ($orangeColors -contains $bgColor) {
                            $cell.Interior.Color = $targetColor
                            $changes++
                            Write-Host "      Alterada cor de fundo: linha $row, coluna $col"
                        }
                    } catch {
                        # Ignorar erros
                    }
                    
                    # Verificar cor da fonte
                    try {
                        $fontColor = $cell.Font.Color
                        if ($orangeColors -contains $fontColor) {
                            $cell.Font.Color = $targetColor
                            $changes++
                            Write-Host "      Alterada cor da fonte: linha $row, coluna $col"
                        }
                    } catch {
                        # Ignorar erros
                    }
                }
            }
        }
        
        $totalChanges += $changes
        Write-Host "    Alterações nesta planilha: $changes"
    }
    
    if ($totalChanges -gt 0) {
        $workbook.Save()
        Write-Host "✓ Arquivo salvo com $totalChanges alterações"
    } else {
        Write-Host "✓ Nenhuma cor laranja encontrada"
    }
    
    $workbook.Close()
    $excel.Quit()
    
    # Limpar objetos COM
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($workbook) | Out-Null
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
    
    Write-Host "Processo concluído!"
    
} catch {
    Write-Host "Erro: $($_.Exception.Message)"
    
    # Tentar fechar Excel em caso de erro
    try {
        if ($workbook) { $workbook.Close() }
        if ($excel) { $excel.Quit() }
    } catch {}
    
    exit 1
}
