# Script PowerShell simples para alterar cores laranja para azul em um arquivo Excel

param(
    [Parameter(Mandatory=$true)]
    [string]$FilePath
)

Write-Host "Processando arquivo: $FilePath"

if (-not (Test-Path $FilePath)) {
    Write-Host "Arquivo não encontrado: $FilePath"
    exit 1
}

try {
    # Criar backup
    $backupPath = "$FilePath.backup"
    if (-not (Test-Path $backupPath)) {
        Copy-Item $FilePath $backupPath
        Write-Host "Backup criado: $backupPath"
    }
    
    # Abrir Excel
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false
    
    $workbook = $excel.Workbooks.Open($FilePath)
    $totalChanges = 0
    
    # Cores específicas para substituir e seus equivalentes em azul
    $colorMappings = @{
        # F4B084 (244, 176, 132) -> Azul claro legível #A4C2F4 (164, 194, 244)
        8696052 = 16040612   # F4B084 -> A4C2F4

        # F7955B (247, 149, 91) -> Azul médio legível #6FA8DC (111, 168, 220)
        5936631 = 14461039   # F7955B -> 6FA8DC
    }

    Write-Host "Mapeamento de cores:"
    Write-Host "  #F4B084 (laranja claro) -> #A4C2F4 (azul claro)"
    Write-Host "  #F7955B (laranja médio) -> #6FA8DC (azul médio)"
    
    Write-Host "Processando $($workbook.Worksheets.Count) planilhas..."
    
    foreach ($worksheet in $workbook.Worksheets) {
        Write-Host "  Planilha: $($worksheet.Name)"
        $changes = 0
        
        $usedRange = $worksheet.UsedRange
        if ($null -ne $usedRange) {
            # Limitar processamento para evitar travamento
            $maxRows = [Math]::Min($usedRange.Rows.Count, 50)
            $maxCols = [Math]::Min($usedRange.Columns.Count, 20)
            
            Write-Host "    Processando $maxRows linhas x $maxCols colunas"
            
            for ($row = 1; $row -le $maxRows; $row++) {
                for ($col = 1; $col -le $maxCols; $col++) {
                    $cell = $usedRange.Cells.Item($row, $col)
                    
                    # Verificar cor de fundo
                    try {
                        $bgColor = $cell.Interior.Color
                        if ($colorMappings.ContainsKey($bgColor)) {
                            $newColor = $colorMappings[$bgColor]
                            $cell.Interior.Color = $newColor
                            $changes++
                            Write-Host "      Alterada cor de fundo: linha $row, coluna $col (cor: $bgColor -> $newColor)"
                        }
                    } catch {
                        # Ignorar erros
                    }

                    # Verificar cor da fonte
                    try {
                        $fontColor = $cell.Font.Color
                        if ($colorMappings.ContainsKey($fontColor)) {
                            $newColor = $colorMappings[$fontColor]
                            $cell.Font.Color = $newColor
                            $changes++
                            Write-Host "      Alterada cor da fonte: linha $row, coluna $col (cor: $fontColor -> $newColor)"
                        }
                    } catch {
                        # Ignorar erros
                    }
                }
            }
        }
        
        $totalChanges += $changes
        Write-Host "    Alterações nesta planilha: $changes"
    }
    
    if ($totalChanges -gt 0) {
        $workbook.Save()
        Write-Host "✓ Arquivo salvo com $totalChanges alterações"
    } else {
        Write-Host "✓ Nenhuma cor laranja encontrada"
    }
    
    $workbook.Close()
    $excel.Quit()
    
    # Limpar objetos COM
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($workbook) | Out-Null
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
    
    Write-Host "Processo concluído!"
    
} catch {
    Write-Host "Erro: $($_.Exception.Message)"
    
    # Tentar fechar Excel em caso de erro
    try {
        if ($workbook) { $workbook.Close() }
        if ($excel) { $excel.Quit() }
    } catch {}
    
    exit 1
}
