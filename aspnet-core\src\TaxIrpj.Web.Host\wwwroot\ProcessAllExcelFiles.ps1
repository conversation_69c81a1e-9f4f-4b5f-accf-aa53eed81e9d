# Script para processar todos os arquivos Excel

Write-Host "Iniciando processamento de todos os arquivos Excel..."

# Diretórios para processar
$directories = @(
    "ModelosImportacao",
    "ModelosImportacao\Ecf",
    "ModelosRelatorios"
)

$totalFiles = 0
$totalChanges = 0
$processedFiles = @()

foreach ($directory in $directories) {
    if (Test-Path $directory) {
        Write-Host "`n=== Processando diretório: $directory ==="
        
        # Buscar arquivos Excel
        $excelFiles = @()
        $excelFiles += Get-ChildItem -Path $directory -Filter "*.xlsx" -File
        $excelFiles += Get-ChildItem -Path $directory -Filter "*.xlsm" -File
        $excelFiles += Get-ChildItem -Path $directory -Filter "*.xls" -File
        
        Write-Host "Encontrados $($excelFiles.Count) arquivos Excel"
        
        foreach ($file in $excelFiles) {
            Write-Host "`nProcessando: $($file.Name)"
            
            try {
                # Executar o script simples para cada arquivo
                $result = & powershell -ExecutionPolicy Bypass -File "SimpleColorChanger.ps1" -FilePath $file.FullName
                
                if ($LASTEXITCODE -eq 0) {
                    $totalFiles++
                    $processedFiles += $file.FullName
                    
                    # Tentar extrair número de alterações do resultado
                    $changeMatch = $result | Select-String "salvo com (\d+) alterações"
                    if ($changeMatch) {
                        $changes = [int]$changeMatch.Matches[0].Groups[1].Value
                        $totalChanges += $changes
                        Write-Host "  ✓ $changes alterações realizadas"
                    } else {
                        Write-Host "  ✓ Processado (nenhuma alteração)"
                    }
                } else {
                    Write-Host "  ✗ Erro ao processar arquivo"
                }
                
            } catch {
                Write-Host "  ✗ Erro: $($_.Exception.Message)"
            }
            
            # Pequena pausa para evitar sobrecarga
            Start-Sleep -Milliseconds 500
        }
    } else {
        Write-Host "Diretório não encontrado: $directory"
    }
}

Write-Host "`n=== RESUMO FINAL ==="
Write-Host "Arquivos processados: $totalFiles"
Write-Host "Total de alterações: $totalChanges"
Write-Host "Cor alterada: laranja → azul (#000646)"

if ($totalChanges -gt 0) {
    Write-Host "`n✓ Processo concluído com sucesso!"
    Write-Host "Backups dos arquivos originais foram criados com extensão .backup"
    
    Write-Host "`nArquivos processados:"
    foreach ($file in $processedFiles) {
        Write-Host "  - $file"
    }
} else {
    Write-Host "`n✓ Processo concluído - nenhuma cor laranja foi encontrada nos arquivos."
}

Write-Host "`nPressione qualquer tecla para continuar..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
