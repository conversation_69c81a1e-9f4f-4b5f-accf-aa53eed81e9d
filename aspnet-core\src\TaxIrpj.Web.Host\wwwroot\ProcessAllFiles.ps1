# Script para processar todos os arquivos Excel alterando cores específicas

Write-Host "=== ALTERAÇÃO DE CORES EM ARQUIVOS EXCEL ==="
Write-Host "Alterando:"
Write-Host "  #F4B084 (laranja claro) -> #A4C2F4 (azul claro)"
Write-Host "  #F7955B (laranja médio) -> #6FA8DC (azul médio)"
Write-Host ""

# Diretórios para processar
$directories = @(
    "ModelosImportacao",
    "ModelosImportacao\Ecf", 
    "ModelosRelatorios"
)

$totalFiles = 0
$totalChanges = 0
$processedFiles = @()
$filesWithChanges = @()

foreach ($directory in $directories) {
    if (Test-Path $directory) {
        Write-Host "=== Processando diretório: $directory ==="
        
        # Buscar arquivos Excel
        $excelFiles = @()
        if (Test-Path $directory) {
            $excelFiles += Get-ChildItem -Path $directory -Filter "*.xlsx" -File -ErrorAction SilentlyContinue
            $excelFiles += Get-ChildItem -Path $directory -Filter "*.xlsm" -File -ErrorAction SilentlyContinue
            $excelFiles += Get-ChildItem -Path $directory -Filter "*.xls" -File -ErrorAction SilentlyContinue
        }
        
        Write-Host "Encontrados $($excelFiles.Count) arquivos Excel"
        
        foreach ($file in $excelFiles) {
            Write-Host "`nProcessando: $($file.Name)"
            $totalFiles++
            
            try {
                # Processar arquivo diretamente (inline)
                $filePath = $file.FullName
                
                # Criar backup
                $backupPath = "$filePath.backup"
                if (-not (Test-Path $backupPath)) {
                    Copy-Item $filePath $backupPath
                    Write-Host "  Backup criado"
                }
                
                # Abrir Excel
                $excel = New-Object -ComObject Excel.Application
                $excel.Visible = $false
                $excel.DisplayAlerts = $false
                
                $workbook = $excel.Workbooks.Open($filePath)
                $fileChanges = 0
                
                # Mapeamento de cores
                $colorMappings = @{
                    8696052 = 16040612   # F4B084 -> A4C2F4
                    5936631 = 14461039   # F7955B -> 6FA8DC
                }
                
                foreach ($worksheet in $workbook.Worksheets) {
                    $usedRange = $worksheet.UsedRange
                    if ($null -ne $usedRange) {
                        $maxRows = [Math]::Min($usedRange.Rows.Count, 100)
                        $maxCols = [Math]::Min($usedRange.Columns.Count, 50)
                        
                        for ($row = 1; $row -le $maxRows; $row++) {
                            for ($col = 1; $col -le $maxCols; $col++) {
                                $cell = $usedRange.Cells.Item($row, $col)
                                
                                # Verificar cor de fundo
                                try {
                                    $bgColor = $cell.Interior.Color
                                    if ($colorMappings.ContainsKey($bgColor)) {
                                        $newColor = $colorMappings[$bgColor]
                                        $cell.Interior.Color = $newColor
                                        $fileChanges++
                                        Write-Host "    Fundo alterado: $($worksheet.Name) [$row,$col]"
                                    }
                                } catch { }
                                
                                # Verificar cor da fonte
                                try {
                                    $fontColor = $cell.Font.Color
                                    if ($colorMappings.ContainsKey($fontColor)) {
                                        $newColor = $colorMappings[$fontColor]
                                        $cell.Font.Color = $newColor
                                        $fileChanges++
                                        Write-Host "    Fonte alterada: $($worksheet.Name) [$row,$col]"
                                    }
                                } catch { }
                            }
                        }
                    }
                }
                
                if ($fileChanges -gt 0) {
                    $workbook.Save()
                    Write-Host "  ✓ $fileChanges alterações salvas"
                    $filesWithChanges += $file.FullName
                } else {
                    Write-Host "  ✓ Nenhuma alteração necessária"
                }
                
                $totalChanges += $fileChanges
                $processedFiles += $file.FullName
                
                $workbook.Close()
                $excel.Quit()
                
                # Limpar objetos COM
                [System.Runtime.Interopservices.Marshal]::ReleaseComObject($workbook) | Out-Null
                [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
                [System.GC]::Collect()
                [System.GC]::WaitForPendingFinalizers()
                
            } catch {
                Write-Host "  ✗ Erro: $($_.Exception.Message)"
                
                # Tentar fechar Excel em caso de erro
                try {
                    if ($workbook) { $workbook.Close() }
                    if ($excel) { $excel.Quit() }
                } catch {}
            }
            
            # Pequena pausa
            Start-Sleep -Milliseconds 200
        }
    } else {
        Write-Host "Diretório não encontrado: $directory"
    }
}

Write-Host "`n" + "="*50
Write-Host "RESUMO FINAL"
Write-Host "="*50
Write-Host "Arquivos processados: $totalFiles"
Write-Host "Total de alterações: $totalChanges"
Write-Host "Arquivos com alterações: $($filesWithChanges.Count)"

if ($filesWithChanges.Count -gt 0) {
    Write-Host "`nArquivos alterados:"
    foreach ($file in $filesWithChanges) {
        $fileName = Split-Path $file -Leaf
        Write-Host "  ✓ $fileName"
    }
    
    Write-Host "`n✓ Processo concluído com sucesso!"
    Write-Host "✓ Backups criados com extensão .backup"
    Write-Host "✓ Cores alteradas: F4B084→A4C2F4, F7955B→6FA8DC"
} else {
    Write-Host "`n✓ Processo concluído - nenhuma das cores específicas foi encontrada."
}

Write-Host "`nPressione Enter para finalizar..."
Read-Host
