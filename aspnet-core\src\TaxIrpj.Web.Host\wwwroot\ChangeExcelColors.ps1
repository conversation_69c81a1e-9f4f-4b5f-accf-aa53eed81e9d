# Script PowerShell simplificado para alterar cores laranja para azul (#000646) em arquivos Excel

# Função para verificar se uma cor é laranja (valores Long do Excel)
function Test-IsOrangeColor {
    param([long]$ColorLong)

    # Lista de cores laranja comuns em formato Long do Excel
    $orangeColorsLong = @(
        16753920,   # Orange (FFA500)
        9109504,    # DarkOrange (FF8C00)
        5275647,    # Coral (FF7F50)
        4678655,    # <PERSON><PERSON> (FF6347)
        17919,      # OrangeRed (FF4500)
        8036607,    # LightSalmon (FFA07A)
        32768,      # Orange variant (FF8000)
        38400,      # Orange variant (FF9500)
        43520,      # Orange variant (FFAA00)
        39168,      # Orange variant (FF9900)
        26112,      # Dark orange (CC6600)
        26368,      # Orange red (FF6600)
        30464,      # Orange variant (FF7700)
        34816,      # Orange variant (FF8800)
        3394815,    # Light orange (FFBB33)
        3355443,    # Orange variant (FF9933)
        3355443     # Orange variant (FF6633)
    )

    return $orangeColorsLong -contains $ColorLong
}

# Função para processar um arquivo Excel
function ProcessExcelFile {
    param(
        [string]$FilePath,
        [long]$TargetColor
    )

    Write-Host "`nProcessando: $FilePath"

    try {
        # Criar backup
        $backupPath = "$FilePath.backup"
        if (-not (Test-Path $backupPath)) {
            Copy-Item $FilePath $backupPath
            Write-Host "  Backup criado: $backupPath"
        }

        # Abrir Excel
        $excel = New-Object -ComObject Excel.Application
        $excel.Visible = $false
        $excel.DisplayAlerts = $false

        $workbook = $excel.Workbooks.Open($FilePath)
        $totalChanges = 0

        # Processar apenas as primeiras 3 planilhas para evitar loops
        $worksheetCount = [Math]::Min($workbook.Worksheets.Count, 3)

        for ($i = 1; $i -le $worksheetCount; $i++) {
            $worksheet = $workbook.Worksheets.Item($i)
            Write-Host "  Processando planilha: $($worksheet.Name)"
            $changes = 0

            $usedRange = $worksheet.UsedRange
            if ($null -ne $usedRange) {
                # Limitar o processamento a 100 linhas e 50 colunas para evitar travamento
                $maxRows = [Math]::Min($usedRange.Rows.Count, 100)
                $maxCols = [Math]::Min($usedRange.Columns.Count, 50)

                for ($row = 1; $row -le $maxRows; $row++) {
                    for ($col = 1; $col -le $maxCols; $col++) {
                        $cell = $usedRange.Cells.Item($row, $col)

                        # Verificar cor de fundo
                        try {
                            $interiorColor = $cell.Interior.Color
                            if ($interiorColor -ne -4142 -and $interiorColor -ne 16777215) {
                                if (Test-IsOrangeColor -ColorLong $interiorColor) {
                                    $cell.Interior.Color = $TargetColor
                                    $changes++
                                    Write-Host "    Alterada cor de fundo da célula $($cell.Address($false, $false))"
                                }
                            }
                        } catch {
                            # Ignorar erros de cor de fundo
                        }

                        # Verificar cor da fonte
                        try {
                            $fontColor = $cell.Font.Color
                            if ($fontColor -ne -4105) {
                                if (Test-IsOrangeColor -ColorLong $fontColor) {
                                    $cell.Font.Color = $TargetColor
                                    $changes++
                                    Write-Host "    Alterada cor da fonte da célula $($cell.Address($false, $false))"
                                }
                            }
                        } catch {
                            # Ignorar erros de cor da fonte
                        }
                    }
                }
            }

            $totalChanges += $changes
        }

        if ($totalChanges -gt 0) {
            $workbook.Save()
            Write-Host "  ✓ Arquivo salvo com $totalChanges alterações"
        } else {
            Write-Host "  ✓ Nenhuma cor laranja encontrada"
        }

        $workbook.Close()
        $excel.Quit()

        # Limpar objetos COM
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($workbook) | Out-Null
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
        [System.GC]::Collect()
        [System.GC]::WaitForPendingFinalizers()

        return $totalChanges

    } catch {
        Write-Host "  ✗ Erro ao processar $FilePath`: $($_.Exception.Message)"

        # Tentar fechar Excel em caso de erro
        try {
            if ($workbook) { $workbook.Close() }
            if ($excel) { $excel.Quit() }
        } catch {}

        return 0
    }
}

# Função principal
function Main {
    Write-Host "Iniciando alteração de cores laranja para azul (#000646) em arquivos Excel..."
    
    # Cor alvo: #000646 (azul) convertida para Long (R + G*256 + B*65536)
    $targetColor = 0 + (6 * 256) + (70 * 65536)  # 4587008
    
    # Diretórios para processar
    $directories = @(
        "ModelosImportacao",
        "ModelosImportacao\Ecf",
        "ModelosRelatorios"
    )
    
    $totalFilesProcessed = 0
    $totalChanges = 0
    
    foreach ($directory in $directories) {
        if (Test-Path $directory) {
            Write-Host "`n=== Processando diretório: $directory ==="
            
            # Buscar arquivos Excel
            $excelFiles = @()
            $excelFiles += Get-ChildItem -Path $directory -Filter "*.xlsx" -File
            $excelFiles += Get-ChildItem -Path $directory -Filter "*.xlsm" -File
            $excelFiles += Get-ChildItem -Path $directory -Filter "*.xls" -File
            
            foreach ($file in $excelFiles) {
                $changes = ProcessExcelFile -FilePath $file.FullName -TargetColor $targetColor
                $totalFilesProcessed++
                $totalChanges += $changes
            }
        } else {
            Write-Host "Diretório não encontrado: $directory"
        }
    }
    
    Write-Host "`n=== RESUMO ==="
    Write-Host "Arquivos processados: $totalFilesProcessed"
    Write-Host "Total de alterações: $totalChanges"
    Write-Host "Cor alvo: #000646 (azul)"
    
    if ($totalChanges -gt 0) {
        Write-Host "`n✓ Processo concluído com sucesso!"
        Write-Host "Backups dos arquivos originais foram criados com extensão .backup"
    } else {
        Write-Host "`n✓ Processo concluído - nenhuma cor laranja foi encontrada nos arquivos."
    }
}

# Executar função principal
Main
