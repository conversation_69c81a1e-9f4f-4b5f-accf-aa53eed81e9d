using System;
using System.IO;
using System.Drawing;
using System.Collections.Generic;
using OfficeOpenXml;
using OfficeOpenXml.Style;

class Program
{
    private static readonly HashSet<string> OrangeColors = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
    {
        "FFA500", "FF8C00", "FF7F50", "FF6347", "FF4500", "FFA07A", 
        "FF8000", "FF9500", "FFAA00", "FF9900", "CC6600", "FF6600",
        "FF7700", "FF8800", "FFBB33", "FF9933", "FF6633", "E67E22",
        "F39C12", "D68910", "DC7633"
    };

    private static readonly Color TargetColor = ColorTranslator.FromHtml("#000646");

    static void Main(string[] args)
    {
        Console.WriteLine("Iniciando alteração de cores laranja para azul (#000646) em arquivos Excel...");
        
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        
        var directories = new[]
        {
            "ModelosImportacao",
            "ModelosImportacao\\Ecf",
            "ModelosRelatorios"
        };

        int totalFilesProcessed = 0;
        int totalChanges = 0;

        foreach (var directory in directories)
        {
            if (Directory.Exists(directory))
            {
                Console.WriteLine($"\n=== Processando diretório: {directory} ===");
                
                var excelFiles = new List<string>();
                excelFiles.AddRange(Directory.GetFiles(directory, "*.xlsx"));
                excelFiles.AddRange(Directory.GetFiles(directory, "*.xlsm"));
                excelFiles.AddRange(Directory.GetFiles(directory, "*.xls"));

                foreach (var filePath in excelFiles)
                {
                    var changes = ProcessExcelFile(filePath);
                    totalFilesProcessed++;
                    totalChanges += changes;
                }
            }
            else
            {
                Console.WriteLine($"Diretório não encontrado: {directory}");
            }
        }

        Console.WriteLine($"\n=== RESUMO ===");
        Console.WriteLine($"Arquivos processados: {totalFilesProcessed}");
        Console.WriteLine($"Total de alterações: {totalChanges}");
        Console.WriteLine($"Cor alvo: #000646 (azul)");
        
        if (totalChanges > 0)
        {
            Console.WriteLine($"\n✓ Processo concluído com sucesso!");
            Console.WriteLine($"Backups dos arquivos originais foram criados com extensão .backup");
        }
        else
        {
            Console.WriteLine($"\n✓ Processo concluído - nenhuma cor laranja foi encontrada nos arquivos.");
        }
    }

    private static int ProcessExcelFile(string filePath)
    {
        Console.WriteLine($"\nProcessando: {filePath}");
        
        try
        {
            var backupPath = filePath + ".backup";
            if (!File.Exists(backupPath))
            {
                File.Copy(filePath, backupPath);
                Console.WriteLine($"  Backup criado: {backupPath}");
            }

            using (var package = new ExcelPackage(new FileInfo(filePath)))
            {
                int totalChanges = 0;

                foreach (var worksheet in package.Workbook.Worksheets)
                {
                    Console.WriteLine($"  Processando planilha: {worksheet.Name}");
                    var changes = ChangeCellColors(worksheet);
                    totalChanges += changes;
                }

                if (totalChanges > 0)
                {
                    package.Save();
                    Console.WriteLine($"  ✓ Arquivo salvo com {totalChanges} alterações");
                }
                else
                {
                    Console.WriteLine($"  ✓ Nenhuma cor laranja encontrada");
                }

                return totalChanges;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"  ✗ Erro ao processar {filePath}: {ex.Message}");
            return 0;
        }
    }

    private static int ChangeCellColors(ExcelWorksheet worksheet)
    {
        int changes = 0;
        var dimension = worksheet.Dimension;
        
        if (dimension == null) return 0;

        for (int row = dimension.Start.Row; row <= dimension.End.Row; row++)
        {
            for (int col = dimension.Start.Column; col <= dimension.End.Column; col++)
            {
                var cell = worksheet.Cells[row, col];
                
                // Verificar cor de fundo
                if (cell.Style.Fill.BackgroundColor.Rgb != null)
                {
                    var currentColor = cell.Style.Fill.BackgroundColor.Rgb;
                    if (IsOrangeColor(currentColor))
                    {
                        cell.Style.Fill.PatternType = ExcelFillStyle.Solid;
                        cell.Style.Fill.BackgroundColor.SetColor(TargetColor);
                        changes++;
                        Console.WriteLine($"    Alterada cor de fundo da célula {GetCellAddress(row, col)}: {currentColor} -> 000646");
                    }
                }

                // Verificar cor da fonte
                if (cell.Style.Font.Color.Rgb != null)
                {
                    var currentColor = cell.Style.Font.Color.Rgb;
                    if (IsOrangeColor(currentColor))
                    {
                        cell.Style.Font.Color.SetColor(TargetColor);
                        changes++;
                        Console.WriteLine($"    Alterada cor da fonte da célula {GetCellAddress(row, col)}: {currentColor} -> 000646");
                    }
                }

                // Verificar bordas
                var borderStyles = new[]
                {
                    cell.Style.Border.Left,
                    cell.Style.Border.Right,
                    cell.Style.Border.Top,
                    cell.Style.Border.Bottom
                };

                foreach (var border in borderStyles)
                {
                    if (border.Color.Rgb != null && IsOrangeColor(border.Color.Rgb))
                    {
                        border.Color.SetColor(TargetColor);
                        changes++;
                        Console.WriteLine($"    Alterada cor da borda da célula {GetCellAddress(row, col)}: {border.Color.Rgb} -> 000646");
                    }
                }
            }
        }

        return changes;
    }

    private static bool IsOrangeColor(string colorHex)
    {
        if (string.IsNullOrEmpty(colorHex)) return false;
        
        // Remove FF prefix se presente
        if (colorHex.Length == 8 && colorHex.StartsWith("FF", StringComparison.OrdinalIgnoreCase))
        {
            colorHex = colorHex.Substring(2);
        }
        
        return OrangeColors.Contains(colorHex);
    }

    private static string GetCellAddress(int row, int col)
    {
        return $"{GetColumnName(col)}{row}";
    }

    private static string GetColumnName(int columnNumber)
    {
        string columnName = "";
        while (columnNumber > 0)
        {
            int modulo = (columnNumber - 1) % 26;
            columnName = Convert.ToChar('A' + modulo) + columnName;
            columnNumber = (columnNumber - modulo) / 26;
        }
        return columnName;
    }
}
